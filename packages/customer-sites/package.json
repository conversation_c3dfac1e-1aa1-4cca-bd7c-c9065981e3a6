{"name": "@imoblr/sites", "version": "0.0.1", "dependencies": {"@astrojs/cloudflare": "^12.3.0", "@astrojs/mdx": "^4.2.1", "@astrojs/react": "^4.2.1", "@fontsource/poppins": "^5.2.5", "@hookform/resolvers": "^4.1.3", "@platform/assets": "workspace:*", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.0.15", "@types/canvas-confetti": "^1.9.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "astro": "^5.7.10", "brazilian-values": "^0.13.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "colorjs.io": "^0.5.2", "cva": "^1.0.0-beta.3", "lucide-react": "^0.483.0", "preline": "^3.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.2.4", "zod": "^3.24.2"}, "scripts": {"dev": "astro dev --host 0.0.0.0", "build": "astro build", "preview": "astro preview"}, "type": "module", "devDependencies": {"@tailwindcss/forms": "^0.5.10"}}