import { memo, useEffect, useRef, useState } from "react";

// Property type mapping from codes to display names
const PROPERTY_TYPE_VALUE_LABEL_MAP = {
  apartment: "Apartamento",
  house: "Casa",
  condominium_house: "Casa de Condomínio",
  penthouse: "Cobertura",
  flat: "Flat",
  studio: "Kitnet / Conjugado",
  lot: "Lote / Terreno",
  townhouse: "Sobrado",
  residential_building: "Edifício Residencial",
  rural_property: "Fazenda / Sítios / Chácaras",
  medical_office: "Consultório",
  warehouse: "Galpão / Depósito / Armazém",
  commercial_property: "Imóvel Comercial",
  commercial_lot: "Lote / Terreno Comercial",
  store: "Ponto Comercial / Loja / Box",
  office: "Sala/Conjunto",
  commercial_building: "Prédio/casa comercial",
};

function PropertyTypeSelectComponent({ primaryColor, teamId, variant = "select" }) {
  console.log("PropertyTypeSelect: Component initialized with teamId:", teamId, "primaryColor:", primaryColor);

  // Add client-side detection log
  if (typeof window !== "undefined") {
    console.log("PropertyTypeSelect: Running on CLIENT SIDE");
  } else {
    console.log("PropertyTypeSelect: Running on SERVER SIDE");
  }

  const [propertyTypes, setPropertyTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [_selectedPropertyType, setSelectedPropertyType] = useState(null);
  const [useNativeSelect, setUseNativeSelect] = useState(false);
  const selectRef = useRef(null);

  // Handler for when a property type is selected
  const handlePropertyTypeChange = (value) => {
    console.log("PropertyTypeSelect: Type selected:", value);
    setSelectedPropertyType(value);

    // Set the global variable for the search button to use
    if (typeof window !== "undefined") {
      window.__selectedPropertyType = value;
      console.log("Set global property type variable:", window.__selectedPropertyType);
    }
  };

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

  // Safely access sessionStorage
  const safeSessionStorage = {
    getItem: (key) => {
      try {
        return isBrowser ? sessionStorage.getItem(key) : null;
      } catch (e) {
        console.warn("Error accessing sessionStorage:", e);
        return null;
      }
    },
    setItem: (key, value) => {
      try {
        if (isBrowser) sessionStorage.setItem(key, value);
      } catch (e) {
        console.warn("Error writing to sessionStorage:", e);
      }
    },
  };

  useEffect(() => {
    console.log("PropertyTypeSelect: useEffect triggered with teamId:", teamId, "typeof window:", typeof window);

    // Function to fetch properties and extract unique types
    const fetchPropertyTypes = async () => {
      console.log("PropertyTypeSelect: fetchPropertyTypes called with teamId:", teamId);

      // Skip if no teamId is provided
      if (!teamId) {
        console.log("PropertyTypeSelect: No teamId provided, skipping fetch");
        return;
      }

      // Check if we have cached property types for this team
      const sessionStorageKey = `propertyTypes_${teamId}`;
      let cachedTypes;

      // Use our safe wrapper to access sessionStorage
      cachedTypes = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedTypes) {
        try {
          const parsedTypes = JSON.parse(cachedTypes);

          // Only use cached types if they're not empty
          if (parsedTypes && parsedTypes.length > 0) {
            setPropertyTypes(parsedTypes);
            return;
          }
        } catch (err) {
          console.error("Error parsing cached property types:", err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-property-types?teamId=${teamId}`;

        console.log(`Fetching property types from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log("API response status:", response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error("API error response:", errorText);
          throw new Error(`Failed to fetch property types: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.propertyTypes || !Array.isArray(data.propertyTypes)) {
          console.warn("No property types found or invalid response format");
          setPropertyTypes([]);
          setIsLoading(false);
          return;
        }

        // Filter out any null or undefined types (shouldn't happen with our API, but just in case)
        const validTypes = data.propertyTypes.filter((type) => type);

        // Update state with the valid types
        setPropertyTypes(validTypes);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(validTypes));
      } catch (err) {
        console.error("Error fetching property types:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchPropertyTypes();
  }, [
    teamId,
    safeSessionStorage.getItem, // Store in sessionStorage to avoid redundant API calls
    safeSessionStorage.setItem,
  ]);

  // Initialize Preline select after component mounts and data changes
  useEffect(() => {
    if (typeof window === "undefined" || !selectRef.current || useNativeSelect) return;

    let retryCount = 0;
    const maxRetries = 20; // Try for 2 seconds
    let cleanupFunction = null;

    // Wait for Preline to be available
    const initializeSelect = () => {
      if (window.HSSelect) {
        try {
          const selectElement = selectRef.current;
          if (selectElement) {
            // Destroy existing instance if it exists
            const existingInstance = window.HSSelect.getInstance(selectElement);
            if (existingInstance) {
              existingInstance.destroy();
            }

            // Force a small delay to ensure DOM is updated
            setTimeout(() => {
              // Initialize the select
              window.HSSelect.autoInit();

              // Add event listener for selection changes
              const handleChange = (e) => {
                const selectedValue = e.detail.payload;
                if (
                  selectedValue &&
                  selectedValue !== "loading" &&
                  selectedValue !== "error" &&
                  selectedValue !== "none"
                ) {
                  handlePropertyTypeChange(selectedValue);
                }
              };

              selectElement.addEventListener("change.hs.select", handleChange);

              cleanupFunction = () => {
                selectElement.removeEventListener("change.hs.select", handleChange);
                const instance = window.HSSelect.getInstance(selectElement);
                if (instance) {
                  instance.destroy();
                }
              };
            }, 10);
          }
        } catch (error) {
          console.warn("Error initializing Preline select:", error);
          setUseNativeSelect(true);
        }
      } else {
        retryCount++;
        if (retryCount < maxRetries) {
          // Retry after a short delay if Preline isn't loaded yet
          setTimeout(initializeSelect, 100);
        } else {
          console.warn("Preline UI not available, falling back to native select");
          setUseNativeSelect(true);
        }
      }
    };

    // Start initialization
    initializeSelect();

    // Cleanup function
    return () => {
      if (cleanupFunction) {
        cleanupFunction();
      }
    };
  }, [useNativeSelect, handlePropertyTypeChange]);

  // Handle native select change
  const handleNativeSelectChange = (e) => {
    const value = e.target.value;
    if (value && value !== "loading" && value !== "error" && value !== "none") {
      handlePropertyTypeChange(value);
    }
  };

  // Render select options based on state
  const renderSelectOptions = () => {
    if (isLoading) {
      return (
        <option value="loading" disabled>
          Carregando...
        </option>
      );
    }

    if (error) {
      return (
        <option value="error" disabled>
          Erro ao carregar tipos
        </option>
      );
    }

    if (propertyTypes && propertyTypes.length > 0) {
      console.log(propertyTypes);
      return propertyTypes.map((type) => (
        <option key={type} value={type}>
          {PROPERTY_TYPE_VALUE_LABEL_MAP[type] || type}
        </option>
      ));
    }

    // Always show at least one selectable option
    return <option value="none">Nenhum imóvel cadastrado</option>;
  };

  // DaisyUI Select Implementation
  // https://daisyui.com/components/select/
  if (variant === "select") {
    return (
      <div className="relative">
        <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
          </svg>
        </div>

        <select
          ref={selectRef}
          onChange={handleNativeSelectChange}
          className="select select-bordered w-full bg-white py-6 pr-4 pl-10 shadow-xl md:shadow-none"
          style={{ "--tw-ring-color": primaryColor }}
          disabled={isLoading}
        >
          <option value="" disabled>
            Tipo de imóvel
          </option>
          {renderSelectOptions()}
        </select>
      </div>
    );
  }

  // DaisyUI Dropdown Implementation
  // https://daisyui.com/components/dropdown/
  return (
    <div className="dropdown dropdown-bottom w-full">
      <div
        tabIndex={0}
        role="button"
        className="btn btn-outline w-full justify-start bg-white py-6 pr-4 pl-10 shadow-xl md:shadow-none"
      >
        <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
          </svg>
        </div>
        <span className="flex-1 text-left">
          {selectedPropertyType
            ? PROPERTY_TYPE_VALUE_LABEL_MAP[selectedPropertyType] || selectedPropertyType
            : "Tipo de imóvel"}
        </span>
        <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <ul
        className="dropdown-content menu z-[1] max-h-72 w-full overflow-y-auto rounded-box bg-base-100 p-2 shadow-lg"
      >
        {isLoading ? (
          <li>
            <span className="text-gray-500">Carregando...</span>
          </li>
        ) : error ? (
          <li>
            <span className="text-error">Erro ao carregar tipos</span>
          </li>
        ) : propertyTypes && propertyTypes.length > 0 ? (
          propertyTypes.map((type) => (
            <li key={type}>
              <a
                onClick={() => handlePropertyTypeChange(type)}
                className={selectedPropertyType === type ? "active" : ""}
              >
                {PROPERTY_TYPE_VALUE_LABEL_MAP[type] || type}
              </a>
            </li>
          ))
        ) : (
          <li>
            <span className="text-gray-500">Nenhum imóvel cadastrado</span>
          </li>
        )}
      </ul>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyTypeSelect = memo(PropertyTypeSelectComponent);
