import { memo, useEffect, useRef, useState } from "react";

function PropertyLocationSelectComponent({ primaryColor, teamId }) {
  console.log("PropertyLocationSelect: Component initialized with teamId:", teamId, "primaryColor:", primaryColor);

  // Add client-side detection log
  if (typeof window !== "undefined") {
    console.log("PropertyLocationSelect: Running on CLIENT SIDE");
  } else {
    console.log("PropertyLocationSelect: Running on SERVER SIDE");
  }

  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [useNativeSelect, setUseNativeSelect] = useState(false);
  const selectRef = useRef(null);

  // Handler for when a location is selected
  const handleLocationChange = (value) => {
    console.log("PropertyLocationSelect: Location selected:", value);
    setSelectedLocation(value);

    // Set the global variable for the search button to use
    if (typeof window !== "undefined") {
      window.__selectedLocation = value;
      console.log("Set global location variable:", window.__selectedLocation);
    }
  };

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

  // Safely access sessionStorage
  const safeSessionStorage = {
    getItem: (key) => {
      try {
        return isBrowser ? sessionStorage.getItem(key) : null;
      } catch (e) {
        console.warn("Error accessing sessionStorage:", e);
        return null;
      }
    },
    setItem: (key, value) => {
      try {
        if (isBrowser) sessionStorage.setItem(key, value);
      } catch (e) {
        console.warn("Error writing to sessionStorage:", e);
      }
    },
  };

  useEffect(() => {
    console.log("PropertyLocationSelect: useEffect triggered with teamId:", teamId, "typeof window:", typeof window);

    // Function to fetch locations and extract unique neighborhoods
    const fetchLocations = async () => {
      console.log("PropertyLocationSelect: fetchLocations called with teamId:", teamId);

      // Skip if no teamId is provided
      if (!teamId) {
        console.log("PropertyLocationSelect: No teamId provided, skipping fetch");
        return;
      }

      // Check if we have cached locations for this team
      const sessionStorageKey = `teamLocations_${teamId}`;
      let cachedLocations;

      // Use our safe wrapper to access sessionStorage
      cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedLocations) {
        try {
          const parsedLocations = JSON.parse(cachedLocations);

          // Only use cached locations if they're not empty
          if (parsedLocations && parsedLocations.length > 0) {
            setLocations(parsedLocations);
            return;
          }
        } catch (err) {
          console.error("Error parsing cached locations:", err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-locations?teamId=${teamId}`;

        console.log(`PropertyLocationSelect: Fetching locations from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log("PropertyLocationSelect: API response status:", response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error("PropertyLocationSelect: API error response:", errorText);
          throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.locations || !Array.isArray(data.locations)) {
          console.warn("PropertyLocationSelect: No locations found or invalid response format");
          setLocations([]);
          setIsLoading(false);
          return;
        }

        // Flatten neighborhoods from all cities into a single array
        const allNeighborhoods = [];
        data.locations.forEach((city) => {
          if (city.neighborhoods && Array.isArray(city.neighborhoods)) {
            city.neighborhoods.forEach((neighborhood) => {
              allNeighborhoods.push(`${neighborhood.name}, ${city.name}`);
            });
          }
        });

        console.log("PropertyLocationSelect: Processed locations:", allNeighborhoods.length);

        // Update state with the flattened neighborhoods
        setLocations(allNeighborhoods);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(allNeighborhoods));
      } catch (err) {
        console.error("PropertyLocationSelect: Error fetching locations:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchLocations();
  }, [teamId, safeSessionStorage.getItem, // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem]);

  // Initialize Preline select after component mounts and data changes
  useEffect(() => {
    if (typeof window === "undefined" || !selectRef.current || useNativeSelect) return;

    let retryCount = 0;
    const maxRetries = 20; // Try for 2 seconds
    let cleanupFunction = null;

    // Wait for Preline to be available
    const initializeSelect = () => {
      if (window.HSSelect) {
        try {
          const selectElement = selectRef.current;
          if (selectElement) {
            // Destroy existing instance if it exists
            const existingInstance = window.HSSelect.getInstance(selectElement);
            if (existingInstance) {
              existingInstance.destroy();
            }

            // Force a small delay to ensure DOM is updated
            setTimeout(() => {
              // Initialize the select
              window.HSSelect.autoInit();

              // Add event listener for selection changes
              const handleChange = (e) => {
                const selectedValue = e.detail.payload;
                if (
                  selectedValue &&
                  selectedValue !== "loading" &&
                  selectedValue !== "error" &&
                  selectedValue !== "none"
                ) {
                  handleLocationChange(selectedValue);
                }
              };

              selectElement.addEventListener("change.hs.select", handleChange);

              cleanupFunction = () => {
                selectElement.removeEventListener("change.hs.select", handleChange);
                const instance = window.HSSelect.getInstance(selectElement);
                if (instance) {
                  instance.destroy();
                }
              };
            }, 10);
          }
        } catch (error) {
          console.warn("Error initializing Preline select:", error);
          setUseNativeSelect(true);
        }
      } else {
        retryCount++;
        if (retryCount < maxRetries) {
          // Retry after a short delay if Preline isn't loaded yet
          setTimeout(initializeSelect, 100);
        } else {
          console.warn("Preline UI not available, falling back to native select");
          setUseNativeSelect(true);
        }
      }
    };

    // Start initialization
    initializeSelect();

    // Cleanup function
    return () => {
      if (cleanupFunction) {
        cleanupFunction();
      }
    };
  }, [useNativeSelect, handleLocationChange]);

  // Handle native select change
  const handleNativeSelectChange = (e) => {
    const value = e.target.value;
    if (value && value !== "loading" && value !== "error" && value !== "none") {
      handleLocationChange(value);
    }
  };

  // Render select options based on state
  const renderSelectOptions = () => {
    if (isLoading) {
      return (
        <option value="loading" disabled>
          Carregando...
        </option>
      );
    }

    if (error) {
      return (
        <option value="error" disabled>
          Erro ao carregar localizações
        </option>
      );
    }

    if (locations && locations.length > 0) {
      return locations.map((location) => (
        <option key={location} value={location}>
          {location}
        </option>
      ));
    }

    // Always show at least one selectable option
    return <option value="none">Nenhuma localização encontrada</option>;
  };

  // DaisyUI Select Implementation
  // https://daisyui.com/components/select/
  if (variant === "select") {
    return (
      <div className="relative">
        <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
              clipRule="evenodd"
            />
          </svg>
        </div>

        <select
          ref={selectRef}
          onChange={handleNativeSelectChange}
          className="select select-bordered w-full bg-white py-6 pr-4 pl-10 shadow-xl md:shadow-none"
          style={{ "--tw-ring-color": primaryColor }}
          disabled={isLoading}
        >
          <option value="" disabled>
            Localização
          </option>
          {renderSelectOptions()}
        </select>
      </div>
    );
  }

  // DaisyUI Dropdown Implementation
  // https://daisyui.com/components/dropdown/
  return (
    <div className="dropdown dropdown-bottom w-full">
      <div
        tabIndex={0}
        role="button"
        className="btn btn-outline w-full justify-start bg-white py-6 pr-4 pl-10 shadow-xl md:shadow-none"
      >
        <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <span className="flex-1 text-left">{selectedLocation || "Localização"}</span>
        <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <ul
        className="dropdown-content menu z-[1] max-h-72 w-full overflow-y-auto rounded-box bg-base-100 p-2 shadow-lg"
      >
        {isLoading ? (
          <li>
            <span className="text-gray-500">Carregando...</span>
          </li>
        ) : error ? (
          <li>
            <span className="text-error">Erro ao carregar localizações</span>
          </li>
        ) : locations && locations.length > 0 ? (
          locations.map((location) => (
            <li key={location}>
              <button
                onClick={() => handleLocationChange(location)}
                className={selectedLocation === location ? "active" : ""}
              >
                {location}
              </button>
            </li>
          ))
        ) : (
          <li>
            <span className="text-gray-500">Nenhuma localização encontrada</span>
          </li>
        )}
      </ul>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyLocationSelect = memo(PropertyLocationSelectComponent);
