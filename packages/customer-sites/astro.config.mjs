// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

import react from '@astrojs/react';

import cloudflare from '@astrojs/cloudflare';

// https://astro.build/config
export default defineConfig({
  output: "server",
  adapter: cloudflare(),
  integrations: [react()],
  image: {
    domains: ["wsrv.nl", "images.unsplash.com"],
    remotePatterns: [{ protocol: "https" }]
  },
  vite: {
    resolve: {
      // Use react-dom/server.edge instead of react-dom/server.browser for React 19.
      // Without this, MessageChannel from node:worker_threads needs to be polyfilled.
      alias: {
        ...(import.meta.env.PROD ? {
          "react-dom/server": "react-dom/server.edge",
        } : {}),
        "@platform/assets": path.resolve("../assets"),
      },
    },
    plugins: [tailwindcss()],
  },

});